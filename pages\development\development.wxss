/* development.wxss */

.container {
  padding: 20rpx;
  background-color: var(--bg-color);
  min-height: 100vh;
}

/* 头部样式 */
.header {
  margin-bottom: 30rpx;
}

.header .card-title {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 10rpx;
}

.header .icon {
  font-size: 40rpx;
  margin-right: 15rpx;
}

.subtitle {
  font-size: 28rpx;
  color: var(--text-secondary);
}

/* 年龄选择器 */
.age-selector {
  margin-bottom: 30rpx;
}

.age-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  padding: 20rpx 0;
}

.age-tab {
  flex: 1;
  min-width: 160rpx;
  padding: 20rpx 15rpx;
  background: white;
  border: 2rpx solid var(--border-color);
  border-radius: 16rpx;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.age-tab.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.age-tab:hover {
  border-color: var(--primary-color);
  transform: translateY(-2rpx);
}

.age-icon {
  display: block;
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.age-text {
  font-size: 24rpx;
  font-weight: 500;
}

.age-tab.active .age-text {
  color: white;
}
/* 发育里程碑 - 优化后的卡片式布局 */
.milestones-section {
  margin-bottom: 30rpx;
}

.milestones-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24rpx;
  padding: 20rpx 0;
}

.milestone-card {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1rpx solid #f0f0f0;
}

.milestone-card-header {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.milestone-card-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.milestone-card-title {
  font-size: 26rpx;
  font-weight: 600;
}

.milestone-card-body {
  padding: 20rpx 24rpx;
}

.milestone-point {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12rpx;
  padding: 8rpx 0;
  position: relative;
}

.milestone-point:last-child {
  margin-bottom: 0;
}

.milestone-point::before {
  content: '•';
  color: #667eea;
  font-weight: bold;
  margin-right: 12rpx;
  font-size: 20rpx;
  line-height: 1.5;
}

.milestone-text {
  flex: 1;
  font-size: 24rpx;
  line-height: 1.6;
  color: #333;
}

/* 响应式：大屏幕显示两列 */
@media (min-width: 750rpx) {
  .milestones-grid {
    grid-template-columns: 1fr 1fr;
  }
}

/* 注意事项 */
.notice-section {
  margin-bottom: 30rpx;
}

.notice-list {
  padding: 20rpx 0;
}

.notice-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding: 15rpx;
  background: #fff3cd;
  border-radius: 12rpx;
  border-left: 6rpx solid #ffc107;
}

.notice-icon {
  font-size: 28rpx;
  margin-right: 15rpx;
  margin-top: 2rpx;
}

.notice-text {
  flex: 1;
  font-size: 26rpx;
  line-height: 1.5;
  color: #856404;
}

/* 发育促进建议 */
.suggestions-section {
  margin-bottom: 30rpx;
}

.suggestions-list {
  padding: 20rpx 0;
}

.suggestion-category {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  color: white;
}

.suggestion-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.suggestion-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.suggestion-title {
  font-size: 28rpx;
  font-weight: bold;
}

.suggestion-content {
  padding-left: 40rpx;
}

.suggestion-text {
  font-size: 26rpx;
  line-height: 1.6;
  opacity: 0.95;
}

/* 相关资源 */
.resources-section {
  margin-bottom: 30rpx;
}

.resources-list {
  padding: 20rpx 0;
}

.resource-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 15rpx;
  background: white;
  border-radius: 12rpx;
  border: 2rpx solid var(--border-color);
  transition: all 0.3s ease;
}

.resource-item:hover {
  border-color: var(--primary-color);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 25rpx rgba(79, 70, 229, 0.15);
}

.resource-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.resource-content {
  flex: 1;
}

.resource-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 5rpx;
}

.resource-desc {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .age-tabs {
    flex-direction: column;
  }

  .age-tab {
    min-width: auto;
  }

  .suggestion-category {
    margin: 0 -10rpx 20rpx;
  }
}

/* 动画效果 */
.milestone-item,
.notice-item,
.suggestion-category,
.resource-item {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 统计信息样式 */
.stats-info {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 分类筛选样式 */
.category-filter {
  margin-bottom: 32rpx;
}

.filter-title {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: flex;
  gap: 16rpx;
  padding-bottom: 8rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  min-width: 100rpx;
  flex-shrink: 0;
}

.category-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: #667eea;
}

.category-icon {
  font-size: 28rpx;
  margin-bottom: 6rpx;
}

.category-name {
  font-size: 22rpx;
  font-weight: 500;
}

/* 热门搜索样式 */
.hot-searches {
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  border-radius: 16rpx;
}

.hot-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #2d3436;
  margin-bottom: 16rpx;
}

.hot-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.hot-item {
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #2d3436;
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.hot-item:active {
  background: white;
  transform: scale(0.95);
}

/* 改进的搜索结果样式 */
.results-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 24rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.filter-info {
  font-size: 22rpx;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.no-results {
  text-align: center;
  padding: 60rpx 32rpx;
  color: #999;
}

.no-results .icon {
  font-size: 48rpx;
  display: block;
  margin-bottom: 16rpx;
}

.no-results .suggestion {
  font-size: 22rpx;
  color: #ccc;
  margin-top: 8rpx;
}

/* 改进的结果项样式 */
.result-item {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border-left: 8rpx solid #667eea;
  position: relative;
  overflow: hidden;
}

.result-item::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-radius: 0 0 0 60rpx;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.behavior-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.behavior-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 6rpx;
}

.behavior-months {
  font-size: 28rpx;
  color: #667eea;
  font-weight: 600;
  background: rgba(102, 126, 234, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.age-group {
  font-size: 22rpx;
  color: #999;
  background: #f8f9fa;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.result-details {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 16rpx;
}

.result-category {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.category-label {
  font-size: 24rpx;
  color: #999;
  margin-right: 8rpx;
}

.category-value {
  font-size: 24rpx;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.result-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  background: #f8f9fa;
  padding: 16rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #667eea;
}

/* 动画效果 */
.result-item {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hot-item,
.category-item {
  animation: fadeIn 0.4s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .behavior-info {
    align-items: flex-end;
  }
  
  .behavior-months {
    font-size: 24rpx;
    padding: 6rpx 12rpx;
  }
  
  .hot-list {
    gap: 8rpx;
  }
  
  .hot-item {
    font-size: 22rpx;
    padding: 6rpx 12rpx;
  }
}

/* 新增：标签页样式 */
.tabs-section {
  margin: 20rpx;
}

.tabs {
  display: flex;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  background: white;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.tab-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.tab-text {
  font-size: 24rpx;
  font-weight: 500;
}

/* 新增：搜索功能样式 */
.search-section {
  margin: 20rpx;
}

.search-box {
  position: relative;
  margin-bottom: 32rpx;
}

.search-input {
  width: 100%;
  height: 80rpx;
  padding: 0 48rpx 0 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 40rpx;
  font-size: 28rpx;
  background: #f8f9fa;
  box-sizing: border-box;
}

.search-input:focus {
  border-color: #667eea;
  background: white;
}

.clear-btn {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 32rpx;
  height: 32rpx;
  background: #ccc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: white;
  cursor: pointer;
}

/* 搜索提示样式 */
.search-tips {
  padding: 32rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border-left: 8rpx solid #667eea;
}

.tips-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.tip-item {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
  line-height: 1.5;
}

/* 搜索结果样式 */
.search-results {
  margin-top: 32rpx;
}

.results-header {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 24rpx;
  text-align: center;
}

.no-results {
  text-align: center;
  padding: 60rpx 32rpx;
  color: #999;
}

.no-results .icon {
  font-size: 48rpx;
  display: block;
  margin-bottom: 16rpx;
}

.result-item {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border-left: 8rpx solid #667eea;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.behavior-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.behavior-months {
  font-size: 28rpx;
  color: #667eea;
  font-weight: 600;
  background: rgba(102, 126, 234, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.result-details {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 16rpx;
}

.result-category {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.category-label {
  font-size: 24rpx;
  color: #999;
  margin-right: 8rpx;
}

.category-value {
  font-size: 24rpx;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.result-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* FAQ样式 */
.faq-section {
  margin: 20rpx;
}

.faq-categories {
  margin-bottom: 32rpx;
}

.faq-category-filter {
  margin-bottom: 24rpx;
}

.faq-category-scroll {
  white-space: nowrap;
}

.faq-category-list {
  display: flex;
  gap: 16rpx;
  padding-bottom: 8rpx;
}

.faq-category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border: 2rpx solid #e0e0e0;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  min-width: 100rpx;
  flex-shrink: 0;
}

.faq-category-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: #667eea;
}

.faq-category-icon {
  font-size: 24rpx;
  margin-bottom: 8rpx;
}

.faq-category-name {
  font-size: 22rpx;
  font-weight: 500;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.faq-item {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1rpx solid #f0f0f0;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.faq-question:active {
  background-color: #f8f9fa;
}

.faq-question-text {
  flex: 1;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.5;
}

.faq-toggle-icon {
  font-size: 32rpx;
  color: #667eea;
  font-weight: bold;
  margin-left: 16rpx;
}

.faq-answer {
  padding: 0 32rpx 24rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  animation: fadeIn 0.3s ease-out;
}

.faq-answer-text {
  font-size: 26rpx;
  line-height: 1.6;
  color: #555;
  display: block;
  margin-bottom: 16rpx;
}

.faq-tips {
  background: #f8f9fa;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #667eea;
}

.tips-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #667eea;
  display: block;
  margin-bottom: 8rpx;
}

.tips-content {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .tab-text {
    font-size: 22rpx;
  }

  .search-input {
    font-size: 26rpx;
  }

  .behavior-name {
    font-size: 28rpx;
  }

  .behavior-months {
    font-size: 24rpx;
  }

  .faq-question-text {
    font-size: 26rpx;
  }

  .faq-answer-text {
    font-size: 24rpx;
  }
}
