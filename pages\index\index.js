// index.js
const calculator = require('../../utils/calculator')

Page({
  data: {
    childCount: 1,
    children: [],
    calculating: false,
    showResult: false,
    results: [],
    totalMonthly: 0,
    totalAmount: 0
  },

  onLoad() {
    this.initializeChildren()
  },

  // 初始化孩子数据
  initializeChildren() {
    const children = []
    for (let i = 1; i <= this.data.childCount; i++) {
      children.push({
        index: i,
        birthDate: ''
      })
    }
    this.setData({ children })
  },

  // 增加孩子数量
  increaseChildCount() {
    if (this.data.childCount < 10) {
      const newCount = this.data.childCount + 1
      this.setData({ childCount: newCount })
      this.updateChildren()
    }
  },

  // 减少孩子数量
  decreaseChildCount() {
    if (this.data.childCount > 1) {
      const newCount = this.data.childCount - 1
      this.setData({ childCount: newCount })
      this.updateChildren()
    }
  },

  // 孩子数量输入变化
  onChildCountChange(e) {
    let count = parseInt(e.detail.value) || 1
    if (count < 1) count = 1
    if (count > 10) count = 10
    
    this.setData({ childCount: count })
    this.updateChildren()
  },

  // 更新孩子数据
  updateChildren() {
    const children = []
    for (let i = 1; i <= this.data.childCount; i++) {
      const existingChild = this.data.children.find(child => child.index === i)
      children.push({
        index: i,
        birthDate: existingChild ? existingChild.birthDate : ''
      })
    }
    this.setData({ 
      children,
      showResult: false // 重置结果显示
    })
  },

  // 出生日期变化
  onBirthDateChange(e) {
    const index = e.currentTarget.dataset.index
    const value = e.detail.value
    const children = [...this.data.children]
    children[index].birthDate = value
    
    this.setData({ children })
  },

  // 计算补贴
  calculateSubsidy() {
    // 验证输入
    const validation = this.validateInput()
    if (!validation.valid) {
      wx.showToast({
        title: validation.message,
        icon: 'none',
        duration: 2000
      })
      return
    }

    // 显示加载状态
    this.setData({ calculating: true })

    // 模拟计算延迟，提升用户体验
    setTimeout(() => {
      try {
        // 准备计算数据
        const childrenData = this.data.children.map(child => ({
          index: child.index,
          birthDate: child.birthDate
        }))

        // 执行计算
        const calculationResult = calculator.calculateTotalSubsidy(childrenData)
        
        // 格式化结果数据
        const formattedResults = calculationResult.results.map(result => ({
          ...result,
          birthDateStr: calculator.formatDate(result.birthDate),
          statusType: this.getStatusType(result.status)
        }))

        // 更新数据
        this.setData({
          results: formattedResults,
          totalMonthly: calculationResult.totalMonthly,
          totalAmount: calculationResult.totalAmount,
          showResult: true,
          calculating: false
        })

        // 显示成功提示
        wx.showToast({
          title: '计算完成',
          icon: 'success',
          duration: 1500
        })

        // 滚动到结果区域
        setTimeout(() => {
          wx.pageScrollTo({
            selector: '.result-section',
            duration: 300
          })
        }, 100)

      } catch (error) {
        console.error('计算过程中出现错误:', error)
        wx.showToast({
          title: '计算失败，请重试',
          icon: 'none',
          duration: 2000
        })
        this.setData({ calculating: false })
      }
    }, 500)
  },

  // 验证输入
  validateInput() {
    for (let i = 0; i < this.data.children.length; i++) {
      const child = this.data.children[i]
      const validation = calculator.validateBirthDate(child.birthDate)
      
      if (!validation.valid) {
        return {
          valid: false,
          message: `第${child.index}个孩子：${validation.message}`
        }
      }
    }
    
    return { valid: true, message: '' }
  },

  // 获取状态类型（用于样式）
  getStatusType(status) {
    if (status === '正在享受补贴') {
      return 'active'
    } else if (status === '补贴已结束' || status === '已超过3岁') {
      return 'ended'
    } else {
      return 'pending'
    }
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '育儿补贴计算器 - 快速计算您的育儿补贴',
      path: '/pages/index/index'
    }
  },

  onShareTimeline() {
    return {
      title: '育儿补贴计算器 - 快速计算您的育儿补贴'
    }
  }
})
