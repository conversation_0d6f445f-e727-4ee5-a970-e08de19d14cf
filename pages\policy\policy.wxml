<!--policy.wxml-->
<view class="container">
  <!-- 头部 -->
  <view class="header">
    <view class="card">
      <view class="card-body text-center">
        <view class="card-title">
          <text class="icon">📋</text>
          <text>政策详细说明</text>
        </view>
        <text class="subtitle">育儿补贴政策完整解读</text>
      </view>
    </view>
  </view>

  <!-- 政策概述 -->
  <view class="policy-overview">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">🎯</text>
          <text>政策概述</text>
        </view>
      </view>
      <view class="card-body">
        <view class="overview-content">
          <text>为支持家庭生育养育，减轻育儿负担，国家自2025年1月1日起实施育儿补贴政策，为3周岁以下婴幼儿家庭提供经济支持。</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 补贴标准 -->
  <view class="subsidy-standard">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">💰</text>
          <text>补贴标准</text>
        </view>
      </view>
      <view class="card-body">
        <view class="standard-list">
          <view class="standard-item">
            <view class="item-title">补贴金额</view>
            <view class="item-content">每孩每年3600元（每月300元）</view>
          </view>
          <view class="standard-item">
            <view class="item-title">补贴对象</view>
            <view class="item-content">3周岁以下婴幼儿</view>
          </view>
          <view class="standard-item">
            <view class="item-title">适用范围</view>
            <view class="item-content">不分一孩、二孩、三孩，均可享受</view>
          </view>
          <view class="standard-item">
            <view class="item-title">补贴期限</view>
            <view class="item-content">从出生开始至满3周岁</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 申请条件 -->
  <view class="application-conditions">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">📝</text>
          <text>申请条件</text>
        </view>
      </view>
      <view class="card-body">
        <view class="conditions-list">
          <view class="condition-item">
            <text class="condition-number">1</text>
            <text class="condition-text">婴幼儿年龄在3周岁以下</text>
          </view>
          <view class="condition-item">
            <text class="condition-number">2</text>
            <text class="condition-text">婴幼儿及其监护人具有本地户籍或居住证</text>
          </view>
          <view class="condition-item">
            <text class="condition-number">3</text>
            <text class="condition-text">家庭收入符合当地相关标准</text>
          </view>
          <view class="condition-item">
            <text class="condition-number">4</text>
            <text class="condition-text">按时提交申请材料并通过审核</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 申请流程 -->
  <view class="application-process">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">🔄</text>
          <text>申请流程</text>
        </view>
      </view>
      <view class="card-body">
        <view class="process-list">
          <view class="process-item">
            <view class="process-step">
              <text class="step-number">1</text>
            </view>
            <view class="process-content">
              <view class="process-title">准备材料</view>
              <view class="process-desc">准备身份证、户口本、出生证明等相关材料</view>
            </view>
          </view>
          <view class="process-item">
            <view class="process-step">
              <text class="step-number">2</text>
            </view>
            <view class="process-content">
              <view class="process-title">提交申请</view>
              <view class="process-desc">到当地社区服务中心或通过线上平台提交申请</view>
            </view>
          </view>
          <view class="process-item">
            <view class="process-step">
              <text class="step-number">3</text>
            </view>
            <view class="process-content">
              <view class="process-title">审核确认</view>
              <view class="process-desc">相关部门进行资格审核，一般15个工作日内完成</view>
            </view>
          </view>
          <view class="process-item">
            <view class="process-step">
              <text class="step-number">4</text>
            </view>
            <view class="process-content">
              <view class="process-title">发放补贴</view>
              <view class="process-desc">审核通过后，补贴将按月发放到指定银行账户</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 注意事项 -->
  <view class="notice">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">⚠️</text>
          <text>注意事项</text>
        </view>
      </view>
      <view class="card-body">
        <view class="notice-list">
          <view class="notice-item">• 补贴政策自2025年1月1日起实施</view>
          <view class="notice-item">• 具体申请时间和流程以当地政府公告为准</view>
          <view class="notice-item">• 补贴金额可能因地区而异，请咨询当地相关部门</view>
          <view class="notice-item">• 如有政策变动，以最新官方公告为准</view>
          <view class="notice-item">• 虚假申报将承担相应法律责任</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 联系方式 -->
  <view class="contact">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">📞</text>
          <text>咨询联系</text>
        </view>
      </view>
      <view class="card-body">
        <view class="contact-info">
          <view class="contact-item">
            <text class="contact-label">咨询热线：</text>
            <text class="contact-value">12345（政务服务热线）</text>
          </view>
          <view class="contact-item">
            <text class="contact-label">办公时间：</text>
            <text class="contact-value">周一至周五 9:00-17:00</text>
          </view>
          <view class="contact-item">
            <text class="contact-label">官方网站：</text>
            <text class="contact-value">请关注当地政府官网</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 返回按钮 -->
  <view class="back-section">
    <button class="btn btn-primary" bindtap="goToCalculator">
      <text class="btn-icon">🧮</text>
      <text>返回计算器</text>
    </button>
  </view>
</view>