<!--development.wxml-->
<view class="container">
  <!-- 头部 -->
  <view class="header">
    <view class="card">
      <view class="card-body text-center">
        <view class="card-title">
          <text class="icon">👶</text>
          <text>婴幼儿发育知识</text>
        </view>
        <text class="subtitle">3岁以内关键发育节点指南</text>
      </view>
    </view>
  </view>

  <!-- 年龄选择器 -->
  <view class="age-selector">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">📅</text>
          <text>选择年龄段</text>
        </view>
      </view>
      <view class="card-body">
        <view class="age-tabs">
          <view 
            wx:for="{{ageGroups}}" 
            wx:key="id" 
            class="age-tab {{selectedAge === item.id ? 'active' : ''}}"
            bindtap="selectAge"
            data-age="{{item.id}}"
          >
            <text class="age-icon">{{item.icon}}</text>
            <text class="age-text">{{item.name}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 标签页切换 -->
  <view class="tabs-section">
    <view class="tabs">
      <view class="tab-item {{currentTab === 'milestones' ? 'active' : ''}}"
            bindtap="switchTab" data-tab="milestones">
        <text class="tab-icon">🎯</text>
        <text class="tab-text">发育里程碑</text>
      </view>
      <view class="tab-item {{currentTab === 'search' ? 'active' : ''}}"
            bindtap="switchTab" data-tab="search">
        <text class="tab-icon">🔍</text>
        <text class="tab-text">行为查询</text>
      </view>
      <view class="tab-item {{currentTab === 'faq' ? 'active' : ''}}"
            bindtap="switchTab" data-tab="faq">
        <text class="tab-icon">❓</text>
        <text class="tab-text">常见问题</text>
      </view>
    </view>
  </view>

  <!-- 发育里程碑 -->
  <view wx:if="{{currentTab === 'milestones'}}" class="milestones-section">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">🎯</text>
          <text>{{currentAgeGroup.name}}发育里程碑</text>
        </view>
      </view>
      <view class="card-body">
        <view class="milestones-grid">
          <view wx:for="{{currentMilestones}}" wx:key="category" class="milestone-card">
            <view class="milestone-card-header">
              <text class="milestone-card-icon">{{item.icon}}</text>
              <text class="milestone-card-title">{{item.category}}</text>
            </view>
            <view class="milestone-card-body">
              <view wx:for="{{item.items}}" wx:for-item="milestone" wx:key="*this" class="milestone-point">
                <text class="milestone-text">{{milestone}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 注意事项 -->
  <view wx:if="{{currentTab === 'milestones'}}" class="notice-section">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">💡</text>
          <text>温馨提示</text>
        </view>
      </view>
      <view class="card-body">
        <view class="notice-list">
          <view class="notice-item">
            <text class="notice-icon">⚠️</text>
            <text class="notice-text">每个孩子的发育节奏不同，以上仅为参考标准</text>
          </view>
          <view class="notice-item">
            <text class="notice-icon">👨‍⚕️</text>
            <text class="notice-text">如有发育迟缓担忧，请及时咨询儿科医生</text>
          </view>
          <view class="notice-item">
            <text class="notice-icon">📚</text>
            <text class="notice-text">多与孩子互动，为其提供丰富的学习环境</text>
          </view>
          <view class="notice-item">
            <text class="notice-icon">❤️</text>
            <text class="notice-text">耐心陪伴，给予孩子充分的爱与支持</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 发育促进建议 -->
  <view wx:if="{{currentTab === 'milestones'}}" class="suggestions-section">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">🌟</text>
          <text>发育促进建议</text>
        </view>
      </view>
      <view class="card-body">
        <view class="suggestions-list">
          <view wx:for="{{currentSuggestions}}" wx:key="category" class="suggestion-category">
            <view class="suggestion-header">
              <text class="suggestion-icon">{{item.icon}}</text>
              <text class="suggestion-title">{{item.category}}</text>
            </view>
            <view class="suggestion-content">
              <text class="suggestion-text">{{item.content}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 相关资源 -->
  <view wx:if="{{currentTab === 'milestones'}}" class="resources-section">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">📖</text>
          <text>相关资源</text>
        </view>
      </view>
      <view class="card-body">
        <view class="resources-list">
          <view class="resource-item" bindtap="openResource" data-type="book">
            <text class="resource-icon">📚</text>
            <view class="resource-content">
              <text class="resource-title">推荐育儿书籍</text>
              <text class="resource-desc">专业的婴幼儿发育指导书籍</text>
            </view>
          </view>
          <view class="resource-item" bindtap="openResource" data-type="app">
            <text class="resource-icon">📱</text>
            <view class="resource-content">
              <text class="resource-title">发育跟踪应用</text>
              <text class="resource-desc">记录宝宝成长里程碑</text>
            </view>
          </view>
          <view class="resource-item" bindtap="openResource" data-type="hospital">
            <text class="resource-icon">🏥</text>
            <view class="resource-content">
              <text class="resource-title">儿科医院</text>
              <text class="resource-desc">专业的儿童健康检查</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 新增：行为查询页面 -->
  <view wx:if="{{currentTab === 'search'}}" class="search-section">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">🔍</text>
          <text>发育行为查询</text>
        </view>
        <view class="stats-info">
          <text>已收录100+个发育行为</text>
        </view>
      </view>
      <view class="card-body">
        <!-- 搜索框 -->
        <view class="search-box">
          <input class="search-input" 
                 placeholder="输入行为关键词，如：拍手、爸爸妈妈、走路..."
                 value="{{searchQuery}}"
                 bindinput="onSearchInput" />
          <view wx:if="{{searchQuery}}" class="clear-btn" bindtap="clearSearch">×</view>
        </view>

        <!-- 分类筛选 -->
        <view class="category-filter">
          <view class="filter-title">按类别筛选：</view>
          <scroll-view class="category-scroll" scroll-x="true">
            <view class="category-list">
              <view wx:for="{{categories}}" wx:key="id"
                    class="category-item {{selectedCategory === item.id ? 'active' : ''}}"
                    bindtap="selectCategory" data-category="{{item.id}}">
                <text class="category-icon">{{item.icon}}</text>
                <text class="category-name">{{item.name}}</text>
              </view>
            </view>
          </scroll-view>
        </view>

        <!-- 热门搜索 -->
        <view wx:if="{{!showSearchResults && !searchQuery}}" class="hot-searches">
          <view class="hot-title">🔥 热门搜索：</view>
          <view class="hot-list">
            <view wx:for="{{hotSearches}}" wx:key="*this"
                  class="hot-item" bindtap="onHotSearchTap" data-keyword="{{item}}">
              {{item}}
            </view>
          </view>
        </view>

        <!-- 搜索提示 -->
        <view wx:if="{{!showSearchResults && !searchQuery}}" class="search-tips">
          <view class="tips-title">💡 使用技巧：</view>
          <view class="tip-item">• 输入具体行为：如"拍手"、"走路"、"说话"</view>
          <view class="tip-item">• 输入发育类别：如"大运动"、"语言"、"社交"</view>
          <view class="tip-item">• 结合分类筛选，快速找到相关行为</view>
          <view class="tip-item">• 点击热门搜索，查看常见发育行为</view>
        </view>

        <!-- 搜索结果 -->
        <view wx:if="{{showSearchResults}}" class="search-results">
          <view wx:if="{{searchResults.length > 0}}" class="results-header">
            <text>找到 {{searchResults.length}} 个相关行为</text>
            <text wx:if="{{selectedCategory !== 'all'}}" class="filter-info">（{{selectedCategory}}类别）</text>
          </view>
          
          <view wx:if="{{searchResults.length === 0}}" class="no-results">
            <text class="icon">😅</text>
            <text>未找到相关行为</text>
            <text class="suggestion">试试调整搜索词或选择"全部"分类</text>
          </view>

          <view wx:for="{{searchResults}}" wx:key="behavior" class="result-item">
            <view class="result-header">
              <text class="behavior-name">{{item.behavior}}</text>
              <view class="behavior-info">
                <text class="behavior-months">{{item.monthsText}}</text>
                <text class="age-group">{{item.ageGroup}}</text>
              </view>
            </view>
            <view class="result-details">
              <view class="result-category">
                <text class="category-label">类别：</text>
                <text class="category-value">{{item.category}}</text>
              </view>
              <view class="result-description">{{item.description}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 常见问题页面 -->
  <view wx:if="{{currentTab === 'faq'}}" class="faq-section">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">❓</text>
          <text>育儿常见问题</text>
        </view>
        <view class="stats-info">
          <text>专业解答，科学育儿</text>
        </view>
      </view>
      <view class="card-body">
        <!-- FAQ分类 -->
        <view class="faq-categories">
          <view class="faq-category-filter">
            <scroll-view class="faq-category-scroll" scroll-x="true">
              <view class="faq-category-list">
                <view wx:for="{{faqCategories}}" wx:key="id"
                      class="faq-category-item {{selectedFaqCategory === item.id ? 'active' : ''}}"
                      bindtap="selectFaqCategory" data-category="{{item.id}}">
                  <text class="faq-category-icon">{{item.icon}}</text>
                  <text class="faq-category-name">{{item.name}}</text>
                </view>
              </view>
            </scroll-view>
          </view>
        </view>

        <!-- FAQ列表 -->
        <view class="faq-list">
          <view wx:for="{{filteredFaqs}}" wx:key="id" class="faq-item">
            <view class="faq-question" bindtap="toggleFaq" data-id="{{item.id}}">
              <text class="faq-question-text">{{item.question}}</text>
              <text class="faq-toggle-icon">{{item.expanded ? '−' : '+'}}</text>
            </view>
            <view wx:if="{{item.expanded}}" class="faq-answer">
              <text class="faq-answer-text">{{item.answer}}</text>
              <view wx:if="{{item.tips}}" class="faq-tips">
                <text class="tips-title">💡 小贴士：</text>
                <text class="tips-content">{{item.tips}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>


