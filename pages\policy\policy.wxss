/* policy.wxss */
.subtitle {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-top: 16rpx;
}

/* 政策概述 */
.policy-overview {
  margin-bottom: 32rpx;
}

.overview-content {
  font-size: 28rpx;
  line-height: 1.8;
  color: var(--text-secondary);
}

/* 补贴标准 */
.subsidy-standard {
  margin-bottom: 32rpx;
}

.standard-list {

}

.standard-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1px solid var(--border-light);
}

.standard-item:last-child {
  border-bottom: none;
}

.item-title {
  font-size: 28rpx;
  color: var(--text-secondary);
  font-weight: 500;
}

.item-content {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 600;
}

/* 申请条件 */
.application-conditions {
  margin-bottom: 32rpx;
}

.conditions-list {

}

.condition-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.condition-number {
  width: 48rpx;
  height: 48rpx;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.condition-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.6;
  flex: 1;
}

/* 申请流程 */
.application-process {
  margin-bottom: 32rpx;
}

.process-list {

}

.process-item {
  display: flex;
  margin-bottom: 32rpx;
  position: relative;
}

.process-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 24rpx;
  top: 60rpx;
  bottom: -32rpx;
  width: 2rpx;
  background: var(--border-color);
}

.process-step {
  margin-right: 24rpx;
  flex-shrink: 0;
}

.step-number {
  width: 48rpx;
  height: 48rpx;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
}

.process-content {
  flex: 1;
}

.process-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.process-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 注意事项 */
.notice {
  margin-bottom: 32rpx;
}

.notice-list {

}

.notice-item {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.8;
  margin-bottom: 16rpx;
}

/* 联系方式 */
.contact {
  margin-bottom: 32rpx;
}

.contact-info {

}

.contact-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid var(--border-light);
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-label {
  font-size: 28rpx;
  color: var(--text-secondary);
  font-weight: 500;
}

.contact-value {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 600;
}

/* 返回按钮 */
.back-section {
  margin-bottom: 32rpx;
}

.back-section .btn {
  width: 100%;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  box-shadow: var(--shadow-md);
}