<!--index.wxml-->
<view class="container">
  <!-- 头部 -->
  <view class="header">
    <view class="card">
      <view class="card-body text-center">
        <view class="card-title">
          <text class="icon">👶</text>
          <text>育儿补贴计算器</text>
        </view>
        <text class="subtitle">计算您的家庭每月和总共可领取的育儿补贴</text>
      </view>
    </view>
  </view>

  <!-- 政策说明 -->
  <view class="policy-section">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">📋</text>
          <text>政策说明</text>
        </view>
      </view>
      <view class="card-body">
        <view class="policy-list">
          <view class="policy-item">• 补贴对象：3周岁以下婴幼儿</view>
          <view class="policy-item">• 补贴标准：每孩每年3600元（每月300元）</view>
          <view class="policy-item">• 适用范围：不分一孩、二孩、三孩</view>
          <view class="policy-item">• 实施时间：2025年1月1日起</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 输入区域 -->
  <view class="input-section">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">👨‍👩‍👧‍👦</text>
          <text>家庭信息</text>
        </view>
      </view>
      <view class="card-body">
        <!-- 孩子数量选择 -->
        <view class="form-group">
          <text class="form-label">孩子数量：</text>
          <view class="number-input">
            <button bindtap="decreaseChildCount" disabled="{{childCount <= 1}}">-</button>
            <input type="number" value="{{childCount}}" bindinput="onChildCountChange" />
            <button bindtap="increaseChildCount" disabled="{{childCount >= 10}}">+</button>
          </view>
        </view>

        <!-- 孩子信息输入 -->
        <view class="children-inputs">
          <view wx:for="{{children}}" wx:key="index" class="child-input">
            <view class="child-header">
              <text class="icon">👶</text>
              <text>第{{item.index}}个孩子</text>
            </view>
            <view class="form-group">
              <text class="form-label">出生日期：</text>
              <picker mode="date" value="{{item.birthDate}}" bindchange="onBirthDateChange" data-index="{{index}}">
                <view class="picker-input">
                  <text wx:if="{{item.birthDate}}">{{item.birthDate}}</text>
                  <text wx:else class="placeholder">请选择出生日期</text>
                </view>
              </picker>
            </view>
          </view>
        </view>

        <!-- 计算按钮 -->
        <button class="btn btn-primary calculate-btn" bindtap="calculateSubsidy" disabled="{{calculating}}">
          <text class="btn-icon">{{calculating ? '⏳' : '🧮'}}</text>
          <text>{{calculating ? '计算中...' : '计算补贴'}}</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 结果展示区域 -->
  <view class="result-section" wx:if="{{showResult}}">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">💰</text>
          <text>补贴计算结果</text>
        </view>
      </view>
      <view class="card-body">
        <!-- 总计 -->
        <view class="result-summary">
          <view class="summary-item">
            <view class="summary-label">每月补贴</view>
            <view class="summary-value">¥{{totalMonthly}}</view>
          </view>
          <view class="summary-item">
            <view class="summary-label">总补贴金额</view>
            <view class="summary-value">¥{{totalAmount}}</view>
          </view>
        </view>

        <!-- 详细结果 -->
        <view class="result-details">
          <view wx:for="{{results}}" wx:key="childIndex" class="child-result">
            <view class="child-result-header">
              <text class="icon">👶</text>
              <text>第{{item.childIndex}}个孩子</text>
            </view>
            <view class="result-info">
              <view class="info-item">
                <text class="label">出生日期：</text>
                <text class="value">{{item.birthDateStr}}</text>
              </view>
              <view class="info-item">
                <text class="label">补贴期间：</text>
                <text class="value">{{item.subsidyPeriod}}</text>
              </view>
              <view class="info-item">
                <text class="label">当前状态：</text>
                <text class="value status-{{item.statusType}}">{{item.status}}</text>
              </view>
              <view class="info-item">
                <text class="label">每月补贴：</text>
                <text class="value">¥{{item.monthlySubsidy}}</text>
              </view>
              <view class="info-item">
                <text class="label">总补贴：</text>
                <text class="value">¥{{item.totalSubsidy}}</text>
              </view>
              <view class="info-item" wx:if="{{item.totalMonths > 0}}">
                <text class="label">补贴月数：</text>
                <text class="value">{{item.totalMonths}}个月</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 温馨提示 -->
        <view class="result-note">
          <view class="note-title">💡 温馨提示：</view>
          <view class="note-item">• 补贴从孩子出生开始计算，直至满3周岁</view>
          <view class="note-item">• 实际发放以当地政策为准</view>
          <view class="note-item">• 建议及时关注当地相关部门通知</view>
        </view>
      </view>
    </view>
  </view>
</view>
