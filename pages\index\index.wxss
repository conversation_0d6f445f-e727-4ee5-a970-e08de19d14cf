/* index.wxss */
.subtitle {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-top: 16rpx;
}

/* 政策说明 */
.policy-section {
  margin-bottom: 32rpx;
}

.policy-list {
  font-size: 28rpx;
  line-height: 1.8;
}

.policy-item {
  margin-bottom: 12rpx;
  color: var(--text-secondary);
}

/* 输入区域 */
.input-section {
  margin-bottom: 32rpx;
}

.children-inputs {
  margin-top: 32rpx;
}

.child-input {
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
}

.child-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.child-header .icon {
  margin-right: 12rpx;
  font-size: 36rpx;
}

.picker-input {
  padding: 24rpx;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  font-size: 32rpx;
  color: var(--text-primary);
}

.placeholder {
  color: var(--text-light);
}

.calculate-btn {
  width: 100%;
  margin-top: 32rpx;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  box-shadow: var(--shadow-md);
}

.calculate-btn[disabled] {
  opacity: 0.6;
  background: var(--text-light);
}

/* 结果区域 */
.result-section {
  margin-bottom: 32rpx;
}

.result-details {
  margin-top: 32rpx;
}

.child-result {
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
}

.child-result-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.child-result-header .icon {
  margin-right: 12rpx;
  font-size: 36rpx;
}

.result-info {
  font-size: 28rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
  line-height: 1.5;
}

.info-item .label {
  color: var(--text-secondary);
  flex-shrink: 0;
}

.info-item .value {
  color: var(--text-primary);
  font-weight: 500;
  text-align: right;
  flex: 1;
  margin-left: 24rpx;
}

.status-active {
  color: var(--secondary-color) !important;
}

.status-ended {
  color: var(--text-light) !important;
}

.status-pending {
  color: var(--accent-color) !important;
}

/* 温馨提示 */
.result-note {
  margin-top: 32rpx;
  padding: 24rpx;
  background: #fef3c7;
  border-radius: var(--radius-lg);
  border-left: 4rpx solid var(--accent-color);
}

.note-title {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16rpx;
}

.note-item {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-bottom: 8rpx;
  line-height: 1.6;
}
