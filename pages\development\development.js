// development.js
Page({
  data: {
    selectedAge: '0-6months',
    currentTab: 'milestones',
    searchQuery: '',
    searchResults: [],
    showSearchResults: false,
    selectedCategory: 'all', // 新增：选中的分类
    selectedFaqCategory: 'all', // 新增：选中的FAQ分类
    filteredFaqs: [], // 新增：筛选后的FAQ列表
    
    // 新增：行为分类
    categories: [
      { id: 'all', name: '全部', icon: '🔍' },
      { id: '大运动', name: '大运动', icon: '🏃' },
      { id: '精细动作', name: '精细动作', icon: '✋' },
      { id: '语言', name: '语言', icon: '💬' },
      { id: '认知', name: '认知', icon: '🧠' },
      { id: '社交', name: '社交', icon: '👥' },
      { id: '生活技能', name: '生活技能', icon: '🍽️' },
      { id: '睡眠', name: '睡眠', icon: '😴' },
      { id: '反射', name: '反射', icon: '⚡' }
    ],

    // 新增：热门搜索词
    hotSearches: [
      '拍手', '爸爸妈妈', '走路', '翻身', '坐立', '爬行',
      '说话', '吃饭', '睡觉', '刷牙', '穿衣', '跳跃'
    ],

    // 新增：FAQ分类
    faqCategories: [
      { id: 'all', name: '全部', icon: '📋' },
      { id: '喂养', name: '喂养', icon: '🍼' },
      { id: '睡眠', name: '睡眠', icon: '😴' },
      { id: '疾病', name: '疾病', icon: '🏥' },
      { id: '发育', name: '发育', icon: '📈' },
      { id: '安全', name: '安全', icon: '🛡️' },
      { id: '护理', name: '护理', icon: '🧼' }
    ],

    // 新增：FAQ数据
    faqData: [
      // 喂养相关
      {
        id: 1,
        category: '喂养',
        question: '新生儿多久喂一次奶？',
        answer: '新生儿通常每2-3小时需要喂奶一次，每天约8-12次。母乳喂养的宝宝可能更频繁，因为母乳消化更快。按需喂养是最好的方式，观察宝宝的饥饿信号。',
        tips: '饥饿信号包括：舔嘴唇、吸吮动作、转头寻找、哭闹等。',
        expanded: false
      },
      {
        id: 2,
        category: '喂养',
        question: '什么时候开始添加辅食？',
        answer: '世界卫生组织建议在宝宝6个月大时开始添加辅食。此时宝宝的消化系统已经发育成熟，能够处理除母乳或配方奶以外的食物。',
        tips: '开始辅食的信号：能坐稳、对食物感兴趣、挺舌反射消失、体重达到出生时的2倍。',
        expanded: false
      },
      {
        id: 3,
        category: '喂养',
        question: '宝宝不爱吃辅食怎么办？',
        answer: '这是很常见的情况。可以尝试：1）多次尝试同一种食物；2）改变食物的质地和形状；3）让宝宝参与进食过程；4）营造愉快的用餐氛围；5）以身作则，和宝宝一起吃。',
        tips: '不要强迫进食，保持耐心。有些宝宝需要尝试10-15次才会接受新食物。',
        expanded: false
      },

      // 睡眠相关
      {
        id: 4,
        category: '睡眠',
        question: '新生儿一天睡多长时间？',
        answer: '新生儿每天需要睡14-17小时，分布在多个睡眠周期中。每次睡眠时间通常为2-4小时。随着宝宝长大，睡眠时间会逐渐减少，但夜间睡眠时间会延长。',
        tips: '建立规律的睡眠环境：安静、黑暗、温度适宜（18-20°C）。',
        expanded: false
      },
      {
        id: 5,
        category: '睡眠',
        question: '如何帮助宝宝建立睡眠规律？',
        answer: '1）建立固定的睡前仪式；2）保持一致的睡眠时间；3）创造舒适的睡眠环境；4）白天多接触自然光；5）避免睡前过度刺激；6）学会识别宝宝的困倦信号。',
        tips: '睡前仪式可以包括：洗澡、换睡衣、读故事、轻柔按摩等。',
        expanded: false
      },
      {
        id: 6,
        category: '睡眠',
        question: '宝宝夜醒频繁怎么办？',
        answer: '夜醒是正常现象，特别是前6个月。应对方法：1）确保宝宝不饿、不湿、不冷不热；2）尝试轻拍安抚而不是立即抱起；3）保持房间昏暗；4）避免过度刺激；5）考虑是否有睡眠倒退期。',
        tips: '4个月、8-10个月、18个月是常见的睡眠倒退期，通常持续2-6周。',
        expanded: false
      },

      // 疾病相关
      {
        id: 7,
        category: '疾病',
        question: '宝宝发烧了怎么办？',
        answer: '3个月以下宝宝发烧需立即就医。3个月以上：1）监测体温；2）保持充足水分；3）适当减少衣物；4）可以使用退烧药（遵医嘱）；5）观察其他症状；6）体温超过39°C或持续不退应就医。',
        tips: '发烧是身体对抗感染的自然反应，不要过度恐慌，但要密切观察。',
        expanded: false
      },
      {
        id: 8,
        category: '疾病',
        question: '如何预防宝宝感冒？',
        answer: '1）勤洗手，特别是接触宝宝前；2）避免接触生病的人；3）保持室内空气流通；4）适当户外活动增强免疫力；5）保证充足睡眠；6）均衡营养；7）按时接种疫苗。',
        tips: '6个月以下的宝宝有来自母体的抗体保护，感冒相对较少。',
        expanded: false
      },

      // 发育相关
      {
        id: 9,
        category: '发育',
        question: '宝宝什么时候会说话？',
        answer: '语言发育时间表：2-3个月开始咿呀学语，6-8个月发出"ba"、"ma"音，10-12个月说出第一个有意义的词，18-24个月词汇量快速增长，能说简单句子。',
        tips: '多和宝宝说话、读书、唱歌可以促进语言发育。每个宝宝发育节奏不同。',
        expanded: false
      },
      {
        id: 10,
        category: '发育',
        question: '担心宝宝发育迟缓怎么办？',
        answer: '如果担心发育问题：1）记录宝宝的发育里程碑；2）与儿科医生讨论；3）进行专业发育评估；4）早期干预很重要；5）不要与其他宝宝过度比较；6）提供丰富的刺激环境。',
        tips: '每个宝宝都有自己的发育节奏，轻微的延迟通常是正常的。',
        expanded: false
      },

      // 安全相关
      {
        id: 11,
        category: '安全',
        question: '如何防止宝宝窒息？',
        answer: '1）避免给小于4岁的宝宝坚果、爆米花等小颗粒食物；2）切碎食物至适当大小；3）监督进食过程；4）学习婴儿急救技能；5）保持玩具清洁，避免小零件；6）睡觉时保持床铺简洁。',
        tips: '学习海姆立克急救法，关键时刻能救命。',
        expanded: false
      },
      {
        id: 12,
        category: '安全',
        question: '家里需要做哪些安全措施？',
        answer: '1）安装安全门栏；2）插座保护盖；3）尖角防护；4）柜门安全锁；5）窗户安全锁；6）收好小物品和危险品；7）固定家具防倾倒；8）楼梯安全网。',
        tips: '宝宝开始爬行后就要开始全面的安全防护，预防胜于治疗。',
        expanded: false
      },

      // 护理相关
      {
        id: 13,
        category: '护理',
        question: '如何给新生儿洗澡？',
        answer: '1）准备温水（37-38°C）；2）先洗脸部，从内眼角向外；3）洗头发，避免水进入耳朵；4）清洗身体，注意皮肤褶皱处；5）动作轻柔快速；6）洗后立即包裹保温；7）脐带未脱落前避免盆浴。',
        tips: '新生儿不需要每天洗澡，每周2-3次即可，重点清洁面部、颈部、手部和尿布区域。',
        expanded: false
      },
      {
        id: 14,
        category: '护理',
        question: '宝宝红屁股怎么处理？',
        answer: '1）保持尿布区域清洁干燥；2）及时更换湿尿布；3）清洗后充分晾干；4）使用护臀膏形成保护层；5）选择透气性好的尿布；6）严重时暂时不用尿布，让皮肤透气。',
        tips: '预防比治疗更重要，每次换尿布时都要仔细清洁和检查。',
        expanded: false
      }
    ],

    ageGroups: [
      { id: '0-6months', name: '0-6个月', icon: '🍼' },
      { id: '6-12months', name: '6-12个月', icon: '👶' },
      { id: '1-2years', name: '1-2岁', icon: '🚶' },
      { id: '2-3years', name: '2-3岁', icon: '🏃' }
    ],
    currentAgeGroup: {},
    currentMilestones: [],
    currentSuggestions: [],

    // 发育里程碑数据
    milestonesData: {
      '0-6months': [
        {
          category: '运动发育',
          icon: '🤸',
          items: [
            '2个月：能抬头45度',
            '3个月：俯卧时能抬头90度',
            '4个月：能翻身（从仰卧到侧卧）',
            '5个月：能坐立（需要支撑）',
            '6个月：能独立坐立片刻'
          ]
        },
        {
          category: '认知发育',
          icon: '🧠',
          items: [
            '2个月：能注视人脸和物体',
            '3个月：能追视移动物体',
            '4个月：对声音有反应',
            '5个月：能认识熟悉的人',
            '6个月：开始有陌生人焦虑'
          ]
        },
        {
          category: '语言发育',
          icon: '🗣️',
          items: [
            '2个月：能发出"啊"、"呜"等声音',
            '3个月：会笑出声',
            '4个月：能发出更多元音',
            '5个月：开始咿呀学语',
            '6个月：能发出"ba"、"ma"等音节'
          ]
        },
        {
          category: '社交发育',
          icon: '👥',
          items: [
            '2个月：会对人微笑',
            '3个月：喜欢与人互动',
            '4个月：会主动寻求关注',
            '5个月：能表达喜怒情绪',
            '6个月：开始模仿简单动作'
          ]
        }
      ],
      '6-12months': [
        {
          category: '运动发育',
          icon: '🤸',
          items: [
            '7个月：能独立坐稳',
            '8个月：开始爬行',
            '9个月：能扶物站立',
            '10个月：能扶物行走',
            '11个月：能独立站立',
            '12个月：开始独立行走'
          ]
        },
        {
          category: '认知发育',
          icon: '🧠',
          items: [
            '7个月：有物体永恒概念',
            '8个月：能理解简单指令',
            '9个月：会玩躲猫猫游戏',
            '10个月：能模仿动作',
            '11个月：理解"不"的含义',
            '12个月：能指认身体部位'
          ]
        },
        {
          category: '语言发育',
          icon: '🗣️',
          items: [
            '7个月：能发出双音节',
            '8个月：开始理解词汇',
            '9个月：会说"mama"、"baba"',
            '10个月：能模仿声音',
            '11个月：理解简单词汇',
            '12个月：能说1-2个有意义的词'
          ]
        },
        {
          category: '社交发育',
          icon: '👥',
          items: [
            '7个月：开始有分离焦虑',
            '8个月：喜欢与其他婴儿互动',
            '9个月：会挥手再见',
            '10个月：喜欢模仿大人',
            '11个月：开始有合作行为',
            '12个月：能表达基本需求'
          ]
        }
      ],
      '1-2years': [
        {
          category: '运动发育',
          icon: '🤸',
          items: [
            '15个月：能独立行走稳定',
            '18个月：能跑步和爬楼梯',
            '21个月：能踢球',
            '24个月：能双脚跳跃'
          ]
        },
        {
          category: '认知发育',
          icon: '🧠',
          items: [
            '15个月：能完成简单拼图',
            '18个月：开始有想象力游戏',
            '21个月：能分类物品',
            '24个月：理解因果关系'
          ]
        },
        {
          category: '语言发育',
          icon: '🗣️',
          items: [
            '15个月：词汇量达到10-20个',
            '18个月：能说50个词汇',
            '21个月：开始组合词汇',
            '24个月：能说2-3个词的句子'
          ]
        },
        {
          category: '社交发育',
          icon: '👥',
          items: [
            '15个月：喜欢独立探索',
            '18个月：开始有自我意识',
            '21个月：能表达情感需求',
            '24个月：开始与同龄人互动'
          ]
        }
      ],
      '2-3years': [
        {
          category: '运动发育',
          icon: '🤸',
          items: [
            '30个月：能骑三轮车',
            '33个月：能单脚站立',
            '36个月：能接球和投球'
          ]
        },
        {
          category: '认知发育',
          icon: '🧠',
          items: [
            '30个月：能数到3',
            '33个月：理解大小概念',
            '36个月：能完成复杂拼图'
          ]
        },
        {
          category: '语言发育',
          icon: '🗣️',
          items: [
            '30个月：词汇量达到300个',
            '33个月：能说完整句子',
            '36个月：能讲简单故事'
          ]
        },
        {
          category: '社交发育',
          icon: '👥',
          items: [
            '30个月：开始学会分享',
            '33个月：能表达复杂情感',
            '36个月：喜欢角色扮演游戏'
          ]
        }
      ]
    },

    // 发育促进建议数据
    suggestionsData: {
      '0-6months': [
        {
          category: '感官刺激',
          icon: '👁️',
          content: '多与宝宝进行眼神交流，使用黑白对比强烈的图片刺激视觉发育。播放轻柔音乐，促进听觉发展。'
        },
        {
          category: '运动训练',
          icon: '🏃',
          content: '每天进行俯卧练习，帮助宝宝练习抬头。轻柔按摩四肢，促进肌肉发育。'
        },
        {
          category: '语言启蒙',
          icon: '💬',
          content: '经常与宝宝说话，描述正在做的事情。重复宝宝发出的声音，鼓励语言发展。'
        },
        {
          category: '情感交流',
          icon: '❤️',
          content: '及时回应宝宝的需求，建立安全感。多拥抱和抚摸，促进亲子关系。'
        }
      ],
      '6-12months': [
        {
          category: '探索环境',
          icon: '🔍',
          content: '提供安全的爬行空间，鼓励宝宝探索。准备不同质地的玩具，刺激触觉发育。'
        },
        {
          category: '精细动作',
          icon: '✋',
          content: '提供适合抓握的小物品，练习手指精细动作。教宝宝用勺子吃饭，培养自理能力。'
        },
        {
          category: '认知游戏',
          icon: '🧩',
          content: '玩躲猫猫游戏，发展物体永恒概念。教宝宝简单的手势，如挥手再见。'
        },
        {
          category: '社交互动',
          icon: '👨‍👩‍👧‍👦',
          content: '安排与其他宝宝的互动时间。建立规律的日常作息，培养安全感。'
        }
      ],
      '1-2years': [
        {
          category: '语言发展',
          icon: '📚',
          content: '每天读绘本给宝宝听，丰富词汇量。鼓励宝宝表达需求，耐心倾听。'
        },
        {
          category: '独立能力',
          icon: '🚶',
          content: '鼓励宝宝自己吃饭、穿衣。提供选择机会，培养决策能力。'
        },
        {
          category: '创造力',
          icon: '🎨',
          content: '提供画笔和纸张，鼓励涂鸦。进行简单的手工活动，发展创造力。'
        },
        {
          category: '规则意识',
          icon: '📏',
          content: '建立简单的家庭规则，培养纪律性。用正面语言引导行为。'
        }
      ],
      '2-3years': [
        {
          category: '学习准备',
          icon: '🎓',
          content: '教授基本的数字和字母。进行分类和配对游戏，发展逻辑思维。'
        },
        {
          category: '社交技能',
          icon: '🤝',
          content: '教宝宝分享和轮流。安排与同龄人的玩耍时间，学习社交技能。'
        },
        {
          category: '情绪管理',
          icon: '😊',
          content: '帮助宝宝识别和表达情绪。教授简单的情绪调节方法。'
        },
        {
          category: '生活技能',
          icon: '🏠',
          content: '让宝宝参与简单的家务活动。培养基本的自理能力和责任感。'
        }
      ]
    },

    // 大幅扩展的发育行为数据库（0-24个月）
    behaviorDatabase: [
      // ===== 0-1个月 =====
      { behavior: '握拳', months: [0, 1], category: '反射', description: '新生儿天生的握拳反射' },
      { behavior: '吸吮', months: [0, 1], category: '反射', description: '天生的吸吮反射，用于进食' },
      { behavior: '惊跳反射', months: [0, 1], category: '反射', description: '对突然声音或动作的惊跳反应' },
      { behavior: '觅食反射', months: [0, 1], category: '反射', description: '触碰脸颊时转头寻找食物' },
      { behavior: '注视光线', months: [0, 1], category: '视觉', description: '能注视明亮的光线或物体' },
      
      // ===== 2-3个月 =====
      { behavior: '微笑', months: [2, 3], category: '社交', description: '对人微笑，表示愉悦和社交互动' },
      { behavior: '抬头', months: [2, 3], category: '大运动', description: '俯卧时能抬头45-90度' },
      { behavior: '追视', months: [2, 3], category: '视觉', description: '眼睛能跟随移动的物体180度' },
      { behavior: '发出声音', months: [2, 3], category: '语言', description: '发出"啊"、"呜"、"咕"等声音' },
      { behavior: '笑出声', months: [3], category: '社交', description: '会发出咯咯的笑声' },
      { behavior: '认识妈妈', months: [2, 3], category: '认知', description: '能区分妈妈和其他人' },
      { behavior: '手张开', months: [2, 3], category: '精细动作', description: '手掌开始张开，不再总是握拳' },
      { behavior: '踢腿', months: [2, 3], category: '大运动', description: '仰卧时会踢腿运动' },
      
      // ===== 4-6个月 =====
      { behavior: '翻身', months: [4, 5, 6], category: '大运动', description: '从仰卧翻到侧卧或俯卧' },
      { behavior: '坐立', months: [5, 6], category: '大运动', description: '需要支撑或能独立坐立片刻' },
      { behavior: '咿呀学语', months: [4, 5, 6], category: '语言', description: '发出更多音节，开始咿呀学语' },
      { behavior: '认识熟人', months: [4, 5, 6], category: '社交', description: '能区分熟悉和陌生的人' },
      { behavior: '抓握物品', months: [4, 5, 6], category: '精细动作', description: '能主动抓握玩具和物品' },
      { behavior: '伸手够物', months: [4, 5], category: '精细动作', description: '看到喜欢的物品会伸手去够' },
      { behavior: '双手合拍', months: [4, 5], category: '精细动作', description: '能将双手合在一起拍打' },
      { behavior: '发ba ma音', months: [5, 6], category: '语言', description: '开始发出"ba"、"ma"等音节' },
      { behavior: '对镜子笑', months: [4, 5, 6], category: '社交', description: '对着镜子中的自己微笑' },
      { behavior: '用嘴探索', months: [4, 5, 6], category: '认知', description: '什么东西都往嘴里放来探索' },
      { behavior: '转头寻声', months: [4, 5, 6], category: '听觉', description: '听到声音会转头寻找声源' },
      { behavior: '喜欢音乐', months: [4, 5, 6], category: '听觉', description: '听到音乐会安静或兴奋' },
      
      // ===== 7-9个月 =====
      { behavior: '独立坐稳', months: [7, 8], category: '大运动', description: '能够独立坐稳不倒' },
      { behavior: '爬行', months: [8, 9], category: '大运动', description: '开始爬行移动，探索环境' },
      { behavior: '拍手', months: [8, 9], category: '精细动作', description: '会拍手，模仿大人动作' },
      { behavior: '说mama baba', months: [8, 9], category: '语言', description: '开始说"mama"、"baba"等音节' },
      { behavior: '躲猫猫', months: [8, 9], category: '认知', description: '喜欢玩躲猫猫游戏，理解物体永恒性' },
      { behavior: '陌生人焦虑', months: [7, 8, 9], category: '社交', description: '对陌生人表现出焦虑或害怕' },
      { behavior: '扔东西', months: [7, 8, 9], category: '精细动作', description: '喜欢故意扔东西，观察结果' },
      { behavior: '撕纸', months: [8, 9], category: '精细动作', description: '能撕纸，锻炼手指精细动作' },
      { behavior: '敲打玩具', months: [7, 8, 9], category: '精细动作', description: '用玩具敲打其他物品制造声音' },
      { behavior: '模仿声音', months: [8, 9], category: '语言', description: '开始模仿大人发出的声音' },
      { behavior: '分离焦虑', months: [7, 8, 9], category: '社交', description: '妈妈离开时会哭闹' },
      { behavior: '用手指抓食物', months: [8, 9], category: '精细动作', description: '能用手指抓小块食物' },
      { behavior: '理解不', months: [8, 9], category: '认知', description: '开始理解"不"的含义' },
      
      // ===== 10-12个月 =====
      { behavior: '扶物站立', months: [9, 10, 11], category: '大运动', description: '能扶着家具站立' },
      { behavior: '扶物行走', months: [10, 11], category: '大运动', description: '扶着家具移步行走' },
      { behavior: '独立站立', months: [11, 12], category: '大运动', description: '能独立站立几秒到几分钟' },
      { behavior: '挥手再见', months: [9, 10, 11], category: '社交', description: '会挥手表示再见' },
      { behavior: '叫爸爸妈妈', months: [10, 11, 12], category: '语言', description: '有意识地叫"爸爸"、"妈妈"' },
      { behavior: '用手指指物', months: [10, 11, 12], category: '认知', description: '用手指指向想要的物品' },
      { behavior: '模仿动作', months: [10, 11, 12], category: '认知', description: '模仿大人的简单动作' },
      { behavior: '钳子抓握', months: [10, 11, 12], category: '精细动作', description: '用拇指和食指抓小物品' },
      { behavior: '放手松开', months: [10, 11], category: '精细动作', description: '能有意识地松手放开物品' },
      { behavior: '找隐藏物', months: [10, 11, 12], category: '认知', description: '能找到被藏起来的玩具' },
      { behavior: '理解简单指令', months: [10, 11, 12], category: '认知', description: '理解"过来"、"坐下"等简单指令' },
      { behavior: '喝杯子', months: [10, 11, 12], category: '生活技能', description: '能用双手拿杯子喝水' },
      { behavior: '撕贴纸', months: [11, 12], category: '精细动作', description: '能撕掉贴在物品上的贴纸' },
      { behavior: '摇头表示不要', months: [11, 12], category: '社交', description: '会摇头表示拒绝' },
      { behavior: '亲吻', months: [11, 12], category: '社交', description: '会主动亲吻家人表达爱意' },
      
      // ===== 13-15个月 =====
      { behavior: '独立行走', months: [12, 13, 14, 15], category: '大运动', description: '能够独立稳定行走，开始学会走路' },
      { behavior: '走路', months: [12, 13, 14, 15], category: '大运动', description: '能够独立走路，不需要扶持' },
      { behavior: '说第一个词', months: [12, 13, 14], category: '语言', description: '说出第一个有意义的词汇' },
      { behavior: '用杯子喝水', months: [12, 13, 14, 15], category: '生活技能', description: '能熟练用杯子喝水' },
      { behavior: '搭积木', months: [13, 14, 15], category: '精细动作', description: '能搭2-3块积木' },
      { behavior: '脱袜子', months: [13, 14, 15], category: '生活技能', description: '能自己脱掉袜子' },
      { behavior: '用勺子', months: [13, 14, 15], category: '生活技能', description: '尝试用勺子吃饭，虽然还不熟练' },
      { behavior: '翻书页', months: [13, 14, 15], category: '精细动作', description: '能翻书的厚页面' },
      { behavior: '投球', months: [14, 15], category: '大运动', description: '能将球投出去' },
      { behavior: '蹲下捡东西', months: [14, 15], category: '大运动', description: '能蹲下捡东西再站起来' },
      { behavior: '模仿家务', months: [13, 14, 15], category: '社交', description: '模仿大人扫地、擦桌子等' },
      { behavior: '认识身体部位', months: [13, 14, 15], category: '认知', description: '能指认眼睛、鼻子、嘴巴等' },
      { behavior: '听懂简单问题', months: [14, 15], category: '认知', description: '听懂"球在哪里？"等简单问题' },
      
      // ===== 16-18个月 =====
      { behavior: '倒退走', months: [16, 17, 18], category: '大运动', description: '能倒退着走几步' },
      { behavior: '爬楼梯', months: [16, 17, 18], category: '大运动', description: '能爬楼梯（需要扶手或大人帮助）' },
      { behavior: '说10-20个词', months: [16, 17, 18], category: '语言', description: '词汇量达到10-20个' },
      { behavior: '用叉子', months: [16, 17, 18], category: '生活技能', description: '尝试用叉子吃饭' },
      { behavior: '搭高积木', months: [16, 17, 18], category: '精细动作', description: '能搭4-6块积木' },
      { behavior: '涂鸦', months: [16, 17, 18], category: '精细动作', description: '能拿笔在纸上涂鸦' },
      { behavior: '开关门', months: [17, 18], category: '精细动作', description: '能转动门把手开关门' },
      { behavior: '模仿电话', months: [16, 17, 18], category: '社交', description: '拿着电话模仿打电话' },
      { behavior: '帮忙穿衣', months: [16, 17, 18], category: '生活技能', description: '穿衣时会配合伸胳膊伸腿' },
      { behavior: '表达需求', months: [16, 17, 18], category: '语言', description: '能用手势或词汇表达基本需求' },
      { behavior: '认识图片', months: [17, 18], category: '认知', description: '能认识书中的动物、物品图片' },
      { behavior: '喜欢音乐', months: [16, 17, 18], category: '社交', description: '听到音乐会摇摆身体' },
      
      // ===== 19-21个月 =====
      { behavior: '跑步', months: [18, 19, 20, 21], category: '大运动', description: '能够跑步，虽然还不够稳' },
      { behavior: '踢球', months: [19, 20, 21], category: '大运动', description: '能踢球并保持平衡' },
      { behavior: '说50个词', months: [18, 19, 20], category: '语言', description: '词汇量达到50个左右' },
      { behavior: '两词组合', months: [20, 21], category: '语言', description: '开始组合两个词汇，如"要水"' },
      { behavior: '自己吃饭', months: [18, 19, 20, 21], category: '生活技能', description: '能用勺子较熟练地自己吃饭' },
      { behavior: '脱简单衣服', months: [19, 20, 21], category: '生活技能', description: '能脱掉简单的衣服' },
      { behavior: '搭积木塔', months: [19, 20, 21], category: '精细动作', description: '能搭6-8块积木的塔' },
      { behavior: '翻薄页书', months: [19, 20, 21], category: '精细动作', description: '能翻书的薄页面' },
      { behavior: '上下楼梯', months: [20, 21], category: '大运动', description: '能上下楼梯（扶着栏杆）' },
      { behavior: '模仿做饭', months: [19, 20, 21], category: '社交', description: '喜欢模仿大人做饭、喂娃娃' },
      { behavior: '认识颜色', months: [20, 21], category: '认知', description: '开始认识红色、蓝色等基本颜色' },
      { behavior: '表达情感', months: [19, 20, 21], category: '社交', description: '能表达开心、生气等基本情感' },
      { behavior: '喜欢独立', months: [19, 20, 21], category: '社交', description: '开始表现出独立意识，说"我自己"' },
      
      // ===== 22-24个月 =====
      { behavior: '双脚跳', months: [22, 23, 24], category: '大运动', description: '能双脚同时离地跳跃' },
      { behavior: '单脚站立', months: [23, 24], category: '大运动', description: '能单脚站立几秒钟' },
      { behavior: '说简单句子', months: [22, 23, 24], category: '语言', description: '能说2-3个词的简单句子' },
      { behavior: '词汇量200+', months: [22, 23, 24], category: '语言', description: '词汇量达到200个以上' },
      { behavior: '穿简单衣服', months: [22, 23, 24], category: '生活技能', description: '能穿简单的衣服，如套头衫' },
      { behavior: '刷牙', months: [22, 23, 24], category: '生活技能', description: '开始学习刷牙（需要帮助）' },
      { behavior: '画圆圈', months: [23, 24], category: '精细动作', description: '能画出圆圈或类似形状' },
      { behavior: '拼简单拼图', months: [22, 23, 24], category: '精细动作', description: '能完成2-4片的简单拼图' },
      { behavior: '骑摇摆车', months: [22, 23, 24], category: '大运动', description: '能骑摇摆车或滑行车' },
      { behavior: '与同龄人玩', months: [22, 23, 24], category: '社交', description: '开始与同龄小朋友一起玩耍' },
      { behavior: '学会分享', months: [23, 24], category: '社交', description: '开始学会分享玩具（偶尔）' },
      { behavior: '认识形状', months: [22, 23, 24], category: '认知', description: '认识圆形、方形等基本形状' },
      { behavior: '数到3', months: [23, 24], category: '认知', description: '能数数到3，理解数量概念' },
      { behavior: '角色扮演', months: [22, 23, 24], category: '社交', description: '喜欢角色扮演游戏，如当医生、老师' },
      { behavior: '表达复杂需求', months: [23, 24], category: '语言', description: '能表达复杂的需求和想法' },
      
      // ===== 特殊行为和技能 =====
      { behavior: '睡整夜觉', months: [3, 4, 5, 6], category: '睡眠', description: '能连续睡6-8小时不醒' },
      { behavior: '白天不睡觉', months: [18, 19, 20, 21, 22, 23, 24], category: '睡眠', description: '白天可能不再需要午睡' },
      { behavior: '控制大小便', months: [18, 19, 20, 21, 22, 23, 24], category: '生活技能', description: '开始有控制大小便的意识' },
      { behavior: '使用便盆', months: [20, 21, 22, 23, 24], category: '生活技能', description: '开始学习使用便盆' },
      { behavior: '洗手', months: [18, 19, 20, 21, 22, 23, 24], category: '生活技能', description: '学会简单的洗手动作' },
      { behavior: '收拾玩具', months: [18, 19, 20, 21, 22, 23, 24], category: '生活技能', description: '能帮忙收拾玩具' },
      { behavior: '听故事', months: [12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24], category: '认知', description: '能安静地听大人讲故事' },
      { behavior: '唱儿歌', months: [18, 19, 20, 21, 22, 23, 24], category: '语言', description: '能跟着唱简单的儿歌' },
      { behavior: '跳舞', months: [16, 17, 18, 19, 20, 21, 22, 23, 24], category: '大运动', description: '听到音乐会跳舞摇摆' },
      { behavior: '安慰他人', months: [18, 19, 20, 21, 22, 23, 24], category: '社交', description: '看到别人哭会表现出关心' },
      { behavior: '记住日常规律', months: [15, 16, 17, 18, 19, 20, 21, 22, 23, 24], category: '认知', description: '记住吃饭、睡觉等日常规律' },
      { behavior: '使用餐具', months: [15, 16, 17, 18, 19, 20, 21, 22, 23, 24], category: '生活技能', description: '熟练使用勺子、叉子等餐具' }
    ]
  },

  onLoad() {
    this.initializeData()
  },

  // 初始化数据
  initializeData() {
    const currentAge = this.data.ageGroups.find(group => group.id === this.data.selectedAge)
    this.setData({
      currentAgeGroup: currentAge,
      currentMilestones: this.data.milestonesData[this.data.selectedAge],
      currentSuggestions: this.data.suggestionsData[this.data.selectedAge],
      filteredFaqs: this.data.faqData
    })
  },

  // 选择年龄段
  selectAge(e) {
    const ageId = e.currentTarget.dataset.age
    const currentAge = this.data.ageGroups.find(group => group.id === ageId)

    this.setData({
      selectedAge: ageId,
      currentAgeGroup: currentAge,
      currentMilestones: this.data.milestonesData[ageId],
      currentSuggestions: this.data.suggestionsData[ageId]
    })
  },

  // 打开资源
  openResource(e) {
    const type = e.currentTarget.dataset.type
    let message = ''

    switch(type) {
      case 'book':
        message = '推荐书籍：《美国儿科学会育儿百科》、《西尔斯亲密育儿百科》等'
        break
      case 'app':
        message = '推荐应用：宝宝树、育儿宝等专业育儿应用'
        break
      case 'hospital':
        message = '建议定期到儿童医院进行健康检查和发育评估'
        break
    }

    wx.showModal({
      title: '资源推荐',
      content: message,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '婴幼儿发育知识 - 关键发育节点指南',
      path: '/pages/development/development'
    }
  },

  onShareTimeline() {
    return {
      title: '婴幼儿发育知识 - 关键发育节点指南'
    }
  },

  // 新增：切换标签页
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({ currentTab: tab })
  },

  // 新增：选择分类
  selectCategory(e) {
    const categoryId = e.currentTarget.dataset.category
    this.setData({ selectedCategory: categoryId })

    // 如果有搜索内容，重新搜索
    if (this.data.searchQuery) {
      this.performSearch(this.data.searchQuery)
    }
  },

  // 新增：搜索输入
  onSearchInput(e) {
    const query = e.detail.value
    this.setData({ searchQuery: query })

    if (query.trim()) {
      this.performSearch(query)
    } else {
      this.setData({
        showSearchResults: false,
        searchResults: []
      })
    }
  },

  // 新增：清除搜索
  clearSearch() {
    this.setData({
      searchQuery: '',
      showSearchResults: false,
      searchResults: []
    })
  },

  // 新增：热门搜索
  onHotSearchTap(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({ searchQuery: keyword })
    this.performSearch(keyword)
  },

  // 改进的搜索功能 - 支持模糊搜索
  performSearch(query) {
    const searchTerms = query.toLowerCase().trim().split(/\s+/)

    let results = this.data.behaviorDatabase.filter(item => {
      const searchText = (item.behavior + ' ' + item.description + ' ' + item.category).toLowerCase()

      // 模糊匹配：只要包含任一搜索词就匹配
      const matchesQuery = searchTerms.some(term => {
        return searchText.includes(term) ||
               this.fuzzyMatch(searchText, term) ||
               this.containsSimilar(searchText, term)
      })

      const matchesCategory = this.data.selectedCategory === 'all' ||
                             item.category === this.data.selectedCategory

      return matchesQuery && matchesCategory
    })

    // 按月龄排序
    results.sort((a, b) => Math.min(...a.months) - Math.min(...b.months))

    // 格式化结果
    results = results.map(item => ({
      ...item,
      monthsText: this.formatMonthsRange(item.months),
      ageGroup: this.getAgeGroup(item.months)
    }))

    this.setData({
      searchResults: results,
      showSearchResults: true
    })
  },

  // 新增：获取年龄段
  getAgeGroup(months) {
    const minMonth = Math.min(...months)
    if (minMonth <= 6) return '0-6个月'
    if (minMonth <= 12) return '6-12个月'
    if (minMonth <= 24) return '1-2岁'
    return '2岁以上'
  },

  // 改进的月龄格式化
  formatMonthsRange(months) {
    if (months.length === 1) {
      return `${months[0]}个月`
    } else {
      const min = Math.min(...months)
      const max = Math.max(...months)
      if (min === max) {
        return `${min}个月`
      }
      return `${min}-${max}个月`
    }
  },

  // 新增：模糊匹配函数
  fuzzyMatch(text, term) {
    // 简单的模糊匹配：允许1-2个字符的差异
    if (term.length < 2) return false

    for (let i = 0; i <= text.length - term.length; i++) {
      let matches = 0
      for (let j = 0; j < term.length; j++) {
        if (text[i + j] === term[j]) {
          matches++
        }
      }
      // 如果匹配度超过80%，认为是模糊匹配
      if (matches / term.length >= 0.8) {
        return true
      }
    }
    return false
  },

  // 新增：相似词匹配
  containsSimilar(text, term) {
    const synonyms = {
      '走': ['行走', '步行', '走路', '迈步'],
      '说': ['讲话', '说话', '言语', '表达'],
      '吃': ['进食', '用餐', '喂食', '吃饭'],
      '睡': ['睡觉', '休息', '入睡', '睡眠'],
      '坐': ['坐立', '端坐', '坐稳'],
      '站': ['站立', '直立', '站稳'],
      '爬': ['爬行', '匍匐', '爬动'],
      '跳': ['跳跃', '蹦跳', '弹跳'],
      '拍': ['拍打', '击打', '拍手'],
      '抓': ['抓握', '握住', '拿取'],
      '笑': ['微笑', '大笑', '咯咯笑'],
      '哭': ['哭泣', '啼哭', '哭闹']
    }

    for (let key in synonyms) {
      if (term.includes(key)) {
        return synonyms[key].some(synonym => text.includes(synonym))
      }
    }
    return false
  },

  // 新增：选择FAQ分类
  selectFaqCategory(e) {
    const categoryId = e.currentTarget.dataset.category
    this.setData({ selectedFaqCategory: categoryId })
    this.filterFaqs()
  },

  // 新增：筛选FAQ
  filterFaqs() {
    let filtered = this.data.faqData
    if (this.data.selectedFaqCategory !== 'all') {
      filtered = this.data.faqData.filter(faq => faq.category === this.data.selectedFaqCategory)
    }
    this.setData({ filteredFaqs: filtered })
  },

  // 新增：展开/收起FAQ
  toggleFaq(e) {
    const faqId = e.currentTarget.dataset.id
    const filteredFaqs = this.data.filteredFaqs.map(faq => {
      if (faq.id === faqId) {
        return { ...faq, expanded: !faq.expanded }
      }
      return faq
    })

    // 同时更新原始数据
    const faqData = this.data.faqData.map(faq => {
      if (faq.id === faqId) {
        return { ...faq, expanded: !faq.expanded }
      }
      return faq
    })

    this.setData({
      filteredFaqs: filteredFaqs,
      faqData: faqData
    })
  },

  // 新增：获取行为统计
  getBehaviorStats() {
    const total = this.data.behaviorDatabase.length
    const categories = {}

    this.data.behaviorDatabase.forEach(item => {
      categories[item.category] = (categories[item.category] || 0) + 1
    })

    return { total, categories }
  }
})
